import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { isBefore } from 'date-fns';
import FlexibleCancellation from './FlexibleCancellation';

jest.mock('date-fns', () => ({
  isBefore: jest.fn(),
}));

jest.mock('components/CancellationRefundSummary/utils', () => ({
  resolveFirstCancellationWindowWithPenalty: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  // eslint-disable-next-line no-unused-vars
  Flex: ({ children, flexWrap, alignItems, alignContent, flexDirection, justifyContent, ...props }) => <div {...props}>{children}</div>,
  Icon: ({ name, ...props }) => <span data-testid={`icon-${name}`} {...props} />,
  TextButton: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
  Text: ({ children, ...props }) => <span {...props}>{children}</span>,
}));

const mockCancellationWindow = {
  startTime: '2025-08-01T00:00:00.000Z',
  formattedBeforeDate: 'August 1, 2025',
};

describe('FlexibleCancellation', () => {
  beforeEach(() => {
    isBefore.mockClear();
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockClear();
  });

  it('should display "Free cancellation" with a clickable button and before date when applicable', async () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);
    const handleOnClick = jest.fn();

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={handleOnClick}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    const freeCancellationButton = screen.getByRole('button', { name: /Free cancellation/i });
    expect(freeCancellationButton).toBeInTheDocument();
    expect(screen.getByText(/before August 1, 2025/i)).toBeInTheDocument();
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
    expect(freeCancellationButton).toBeEnabled();

    await userEvent.click(freeCancellationButton);
    expect(handleOnClick).toHaveBeenCalledTimes(1);
  });

  it('should display "Free cancellation" as plain text when no click handler is provided', () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    expect(screen.getByText('Free cancellation')).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Free cancellation/i })).not.toBeInTheDocument();
    expect(screen.getByText(/before August 1, 2025/i)).toBeInTheDocument();
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
  });

  it('should display "Free cancellation" and hide the "before date" when hideBeforeDate is true', () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);
    const handleOnClick = jest.fn();

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={handleOnClick}
        fontSize="16px"
        hideBeforeDate={true}
        hideWhenNonRefundable={false}
      />,
    );

    expect(screen.getByRole('button', { name: /Free cancellation/i })).toBeInTheDocument();
    expect(screen.queryByText(/before August 1, 2025/i)).not.toBeInTheDocument();
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
  });

  it('should display "Cancellation Policy" with a clickable button when past free cancellation window and handleOnClick is provided', async () => {
    isBefore.mockReturnValue(false);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);
    const handleOnClick = jest.fn();

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={handleOnClick}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    const cancellationPolicyButton = screen.getByRole('button', { name: /Cancellation Policy/i });
    expect(cancellationPolicyButton).toBeInTheDocument();
    expect(screen.getByTestId('icon-infoOutline')).toBeInTheDocument();
    expect(cancellationPolicyButton).toBeEnabled();

    await userEvent.click(cancellationPolicyButton);
    expect(handleOnClick).toHaveBeenCalledTimes(1);
  });

  it('should display "Cancellation Policy" as plain text when past free cancellation window and no click handler is provided', () => {
    isBefore.mockReturnValue(false);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    expect(screen.getByText('Cancellation Policy')).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Cancellation Policy/i })).not.toBeInTheDocument();
    expect(screen.getByTestId('icon-infoOutline')).toBeInTheDocument();
  });

  it('should render null when hideWhenNonRefundable is true and current date is after cancellation window', () => {
    isBefore.mockReturnValue(false);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);

    const { container } = render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={true}
      />,
    );

    expect(container).toBeEmptyDOMElement();
  });

  it('should display "Free cancellation" and not the "before date" if no cancellation window with penalty is found', () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(null);

    render(
      <FlexibleCancellation
        cancellationWindows={[]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    expect(screen.getByText('Free cancellation')).toBeInTheDocument();
    expect(screen.queryByText(/before/i)).not.toBeInTheDocument();
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
  });

  it('should display correct icon for "Free cancellation" state', () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={jest.fn()}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-infoOutline')).not.toBeInTheDocument();
  });

  it('should display correct icon for "Cancellation Policy" state', () => {
    isBefore.mockReturnValue(false);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={jest.fn()}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );
    expect(screen.getByTestId('icon-infoOutline')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-freeCancellation')).not.toBeInTheDocument();
  });

  it('should handle keyboard interaction for "Free cancellation" button', async () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);
    const handleOnClick = jest.fn();

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={handleOnClick}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    const freeCancellationButton = screen.getByRole('button', { name: /Free cancellation/i });
    freeCancellationButton.focus();
    expect(freeCancellationButton).toHaveFocus();

    await userEvent.keyboard('{enter}');
    expect(handleOnClick).toHaveBeenCalledTimes(1);
  });

  it('should handle keyboard interaction for "Cancellation Policy" button', async () => {
    isBefore.mockReturnValue(false);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);
    const handleOnClick = jest.fn();

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={handleOnClick}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    const cancellationPolicyButton = screen.getByRole('button', { name: /Cancellation Policy/i });
    cancellationPolicyButton.focus();
    expect(cancellationPolicyButton).toHaveFocus();

    await userEvent.keyboard('{enter}');
    expect(handleOnClick).toHaveBeenCalledTimes(1);
  });

  it('should not call handleOnClick if the component renders as text (not a button)', async () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(mockCancellationWindow);
    const handleOnClick = jest.fn();

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    const freeCancellationText = screen.getByText('Free cancellation');
    await userEvent.click(freeCancellationText);
    expect(handleOnClick).not.toHaveBeenCalled();
  });

  it('should display "Free cancellation" when current window has zero nights, zero percentage, and zero fixedCost penalty', () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue({
      ...mockCancellationWindow,
      nights: 0,
      percentage: 0,
      fixedCost: 0,
    });

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    expect(screen.getByText('Free cancellation')).toBeInTheDocument();
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
  });

  [{ fixedCost: '1' }, { percentage: '10%' }, { nights: '1' }].forEach((penalty) => {
    it(`should display "Cancellation Policy" with a penalty of ${JSON.stringify(penalty)} on the current window`, () => {
      isBefore.mockReturnValue(false);
      require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue({
        ...mockCancellationWindow,
        ...penalty,
      });

      render(
        <FlexibleCancellation
          cancellationWindows={[{}]}
          handleOnClick={null}
          fontSize="16px"
          hideBeforeDate={false}
          hideWhenNonRefundable={false}
        />,
      );

      expect(screen.getByText('Cancellation Policy')).toBeInTheDocument();
      expect(screen.getByTestId('icon-infoOutline')).toBeInTheDocument();
    });
  });

  it('should display "Free cancellation" when there is no window with a penalty', () => {
    isBefore.mockReturnValue(true);
    require('components/CancellationRefundSummary/utils').resolveFirstCancellationWindowWithPenalty.mockReturnValue(null);

    render(
      <FlexibleCancellation
        cancellationWindows={[{}]}
        handleOnClick={null}
        fontSize="16px"
        hideBeforeDate={false}
        hideWhenNonRefundable={false}
      />,
    );

    expect(screen.getByText('Free cancellation')).toBeInTheDocument();
    expect(screen.getByTestId('icon-freeCancellation')).toBeInTheDocument();
    expect(screen.queryByText(/before/i)).not.toBeInTheDocument();
  });
});
