[![Build status](https://badge.buildkite.com/5901a37d8755a28a4d7b5e94e372a6956eeba6291adb474687.svg)](https://buildkite.com/hooroo/qantas-hotels-ui)

# Qantas Hotels UI

A Next.js-based React application for the Qantas Hotels booking platform, featuring server-side rendering, TypeScript support, and comprehensive testing.

## Tech Stack

- **Framework**: Next.js, React
- **Language**: TypeScript, JavaScript
- **Styling**: Emotion CSS, Styled System
- **State Management**: Redux Toolkit, Redux Logic
- **Testing**: Jest, Cypress, React Testing Library
- **Build**: Webpack, Babel, ESBuild
- **Deployment**: Buildkite, Docker, AWS ECS/S3
- **Monitoring**: Sentry

## Contents

- [Getting Started](#getting-started)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Architecture](#architecture)
- [Deployed Environments](https://github.com/hooroo/qantas-hotels-ui/wiki#environments)
- [FAQs](#faqs)
- [Contributing](https://github.com/hooroo/qantas-hotels-ui/blob/master/.github/CONTRIBUTING.md)

## Getting Started

### Prerequisites

Ensure the following software is installed:

- **Git** - Version control
- **Node.js 22** - Runtime environment (see `.nvmrc` for exact version)
- **Node Version Manager (nvm)** - Node version management
- **Yarn** - Package manager

### Quick Start

```bash
git clone https://github.com/hooroo/qantas-hotels-ui
cd qantas-hotels-ui
nvm install && nvm use
yarn install
yarn start
```

Navigate to http://localhost:3000 to view the application.

**Note:** For full functionality, you'll also need to run the `qantas-hotels-api` backend service. The frontend proxies API requests to the backend during development (see `next.config.js` rewrites configuration). Or, you can point `NEXT_PUBLIC_HOTELS_API_HOST` in `.env.development` to [staging](https://staging-hotels-qantas-akamai.jqdev.net/hotels).

### Environment Setup

The application uses environment-specific configurations:

- `.env.development` - Local development
- `.env.test` - Test environment
- `.env.e2e` - End-to-end testing

Key environment variables:

- `NEXT_PUBLIC_HOTELS_API_HOST` - API host URL
- `NEXT_PUBLIC_HOTELS_API_BASE_PATH` - API base path
- `SENTRY_AUTH_TOKEN` - Sentry error tracking token

## Development

To run locally, run `yarn start` & navigate to http://localhost:3000.

### Deploying to Staging

The Buildkite pipeline automatically deploys to staging for branches matching these patterns:

- `master` - Production and staging deployments
- `test/*` - Test branches (staging only)

## Deployment

### Automated Deployments

The Buildkite pipeline handles deployments to multiple environments:

**Environments:**

- **Staging** - `master`, `test/*` branches
- **SIT** - `master` branch only
- **Production** - `master` branch only

**Pipeline Steps:**

1. Build base Docker image
2. Run linting and tests (browser/server)
3. Build environment-specific artifacts
4. Create runtime Docker images
5. Deploy to S3 and ECS
6. Run E2E tests
7. Tag images

### Manual Deployment

For emergency deployments or specific builds:

```bash
# Build for development environment
yarn build:dev

# Build for production environment
yarn build
```

### Infrastructure

- **Containerisation**: Docker with multi-stage builds
- **Orchestration**: AWS ECS for container management
- **CDN**: S3 for static asset hosting
- **Monitoring**: Sentry for error tracking and performance

## Testing

### Unit Tests

The project has separate test suites for browser and server contexts:

```bash
# Run all tests
yarn test:browser          # DOM context tests
yarn test:server           # Server context tests

# Watch mode for development
yarn test:browser:watch    # Watch browser tests
yarn test:server:watch     # Watch server tests

# Run only changed tests
yarn test:browser:fast    # Only changed files
yarn test:server:fast     # Only changed files
```

#### Sharded Browser Tests

For faster test execution, browser tests can be run in parallel shards:

```bash
# Run sharded browser tests in parallel (defaults to 2 shards)
yarn test:browser:shard     # Run 2 shards in parallel
yarn test:browser:shard 4   # Run 4 shards in parallel
yarn test:browser:shard 8   # Run 8 shards in parallel
```

The script automatically runs all shards in parallel and waits for completion. It includes validation and helpful error messages for invalid configurations.

### End-to-End Tests

E2E tests use [Cypress](https://docs.cypress.io) with Chrome:

```bash
yarn test:e2e              # Run against staging (headless)
yarn test:e2e:dev          # Run against localhost:3000 (GUI mode)
yarn test:e2e:parallel     # Run tests in parallel
```

#### Configurable E2E Pipeline

The E2E test pipeline can be configured to run specific test files instead of the entire suite:

**Via Buildkite UI:**

1. Go to the [qantas-hotels-ui-e2e-tests pipeline](https://buildkite.com/hooroo/qantas-hotels-ui-e2e-tests)
2. Click "New Build"
3. Add environment variable `CYPRESS_SPECS` with your desired pattern

**Via Command Line:**

```bash
# Run specific test file
CYPRESS_SPECS="cypress/e2e/checkout/checkoutPaymentForm.spec.js" buildkite-agent pipeline upload .buildkite/pipeline.smoke.sh

# Run multiple specific files (comma-separated)
CYPRESS_SPECS="cypress/e2e/checkout/checkoutPaymentForm.spec.js,cypress/e2e/qta/qtaHeaderAndFooter.spec.js" buildkite-agent pipeline upload .buildkite/pipeline.smoke.sh

# Run all tests in a directory
CYPRESS_SPECS="cypress/e2e/checkout/*.spec.js" buildkite-agent pipeline upload .buildkite/pipeline.smoke.sh

# Run all tests (default behavior)
buildkite-agent pipeline upload .buildkite/pipeline.smoke.sh
```

**Common Patterns:**

- `cypress/e2e/checkout/*.spec.js` - All checkout tests
- `cypress/e2e/**/checkoutPayment*.spec.js` - All checkout payment-related tests
- `cypress/e2e/general/pointsClub.spec.js` - Single specific test

### Test Configuration

- **Jest**: Browser and server test configurations in `jest/`
- **Cypress**: Configuration in `cypress.config.ts`
- **Coverage**: HTML reports for Jest are generated in `coverage/` directory when running `yarn test:browser:cov` and `yarn test:server:cov`.

### Accessibility

We need to achieve [AA Conformance to Web Content Accessibility Guidelines 2.1](https://www.w3.org/WAI/WCAG21/quickref/?versions=2.1)

- **`jest-axe`** is used to test components in unit tests.
- **`axe-core/react`** is enabled in development to log accessibility warnings to the console.
- **`eslint-plugin-jsx-a11y`** rules are used during linting.
- **`Pa11y-CI`** is ran in CI/CD against staging URLs, using both Axe and HTML_CodeSniffer engines:

```bash
yarn test:a11y
```

### Responsive UI

SSR (Server-side rendering) requires a CSS-only media-query approach.

- Breakpoints are set in `/lib/theme.js`
- [Styled System style utilities](https://github.com/jxnblk/styled-system/blob/master/docs/responsive-styles.md) use props that accept arrays as values for mobile-first responsive styles
- To hide and show based on screen size use the [`<Hide>` component](https://hooroo.github.io/roo-ui/?knob-Assets=static%2Fmedia%2Froo.0b6134e0.svg&selectedKind=Components%7CHide&selectedStory=default&full=0&addons=1&stories=1&panelRight=1&addonPanel=storybooks%2Fstorybook-addon-knobs) from Roo-UI

## Architecture

### Project Structure

```bash
src
├── api/           # API integration layer
├── components/    # Reusable React components
├── config/        # Application configuration
├── hooks/         # Custom React hooks
├── layouts/       # Page layout components
├── lib/           # Utility libraries and helpers
├── pages/         # Next.js pages (file-based routing)
├── store/         # Redux store configuration
├── types/         # TypeScript type definitions
└── server.js      # Express server setup
```

### Key Features

- **Server-Side Rendering (SSR)** - Next.js with custom Express server
- **Code Splitting** - Automatic route-based splitting with `@loadable/component`
- **Responsive Design** - Mobile-first approach with styled-system breakpoints
- **Accessibility** - WCAG 2.0 AA compliance with automated testing
- **Performance Monitoring** - Sentry integration for error tracking
- **API Proxy** - Development API proxying to avoid CORS issues

### Development Tools

**Code Quality:**

```bash
yarn lint                 # Run ESLint
yarn quickLint            # Auto-fix linting issues
```

**Development Server:**

```bash
yarn start                # Start development server
yarn dev                  # Alias for yarn start
yarn serve                # Production server locally
```

## FAQs

### Technical Questions

**Why set `--max-http-header-size=16000`?**
Cookies on www.qantas.com exceed Node.js's default 8KB header limit. The limit is increased to 16KB to align with AWS ALB limits. This may be reduced after the Old Hotels migration is complete.

**Why use both TypeScript and JavaScript?**
The project is in gradual migration to TypeScript. New files should use TypeScript (`.ts`/`.tsx`), while existing JavaScript files are migrated incrementally.

Also, some files must remain JavaScript due to specific constraints, such as Cypress integration issues. For example, `AppLink.js` cannot be TypeScript because it's imported in navigation config files that Cypress also imports. Cypress cannot parse TypeScript in these shared config files without a full overhaul of the test infrastructure.

**What's the difference between browser and server tests?**

- **Browser tests**: Test components in DOM context with JSDOM
- **Server tests**: Test server-side rendering and API logic

### Development Questions

**How do I add a new page?**
Create a file in `src/pages/` with `.page.js` or `.page.tsx` extension. Next.js will automatically create the route.

**How do I add environment variables?**

- Client-side: Prefix with `NEXT_PUBLIC_` in `.env.development`
- Server-side: Add to environment files without prefix

**Why are some imports failing?**
The project uses absolute imports with `src/` as the base directory. Import from the root: `import Component from 'components/Component'`

### Deployment Questions

**How do I deploy a hotfix?**
Create a pull request to trigger staging deployment, then merge to master for production deployment.

**What happens if tests fail?**
The pipeline will stop and prevent deployment. Check the Buildkite logs for specific test failures.

**How do I check deployment status?**
Monitor the [Buildkite dashboard](https://buildkite.com/hooroo/qantas-hotels-ui) or check the [#concierge-build-alerts](https://qantashotels.slack.com/archives/C0985TXR9JP) Slack channel for notifications.
