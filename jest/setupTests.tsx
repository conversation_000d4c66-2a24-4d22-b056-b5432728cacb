/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-env jest */
/* eslint-disable no-console */
/* eslint-disable react/prop-types */
import React from 'react';
import ReactModal from 'react-modal';
import { matchers } from 'jest-emotion';
import { act } from 'react-dom/test-utils';
import { loadEnvConfig } from '@next/env';
import failOnConsole from 'jest-fail-on-console';

import crypto from 'crypto';

import 'jest-canvas-mock';
import '@testing-library/jest-dom/jest-globals';
import 'core-js/es/array/flat';

// Configure jest-fail-on-console to fail on ALL types of console logs
failOnConsole({
  shouldFailOnAssert: true,
  shouldFailOnDebug: true,
  shouldFailOnError: true,
  shouldFailOnInfo: true,
  shouldFailOnLog: true,
  shouldFailOnWarn: true,
});

require('dotenv').config({
  path: './.env.test',
});
loadEnvConfig(process.cwd(), false, {
  ...console,
  info() {
    // suppress log output for "Loaded env from ..."
  },
});

declare global {
  interface Window {
    qff_auth: object;
  }
}

expect.extend(matchers);

if (typeof document !== 'undefined') {
  Object.defineProperty(document, 'fonts', {
    value: {
      check: jest.fn().mockImplementation(() => {
        return true;
      }),
    },
  });
}

if (typeof global.self !== 'undefined') {
  Object.defineProperty(global.self, 'crypto', {
    value: {
      getRandomValues: (arr) => crypto.randomBytes(arr.length),
    },
  });
}

if (typeof window !== 'undefined') {
  window.qff_auth = {
    showLoginModal: jest.fn(),
    logout: jest.fn(),
    isInitialised: jest.fn(),
    subscribeInitCompleted: jest.fn(),
    subscribeLoginSuccess: jest.fn(),
    subscribeLoginFailure: jest.fn(),
    subscribeLogoutSuccess: jest.fn(),
    subscribeSyncProfileUpdate: jest.fn(),
  };

  // Provide a minimal, no-op IntersectionObserver to avoid asynchronous callbacks
  // firing outside of React Testing Library's act(...) during tests.
  class MockIntersectionObserver {
    readonly root: Element | Document | null = null;
    readonly rootMargin: string = '';
    readonly thresholds: ReadonlyArray<number> = [];
    observe() {}
    unobserve() {}
    disconnect() {}
    takeRecords(): IntersectionObserverEntry[] {
      return [];
    }
  }

  Object.defineProperty(window, 'IntersectionObserver', {
    writable: true,
    configurable: true,
    value: MockIntersectionObserver,
  });

  // Also expose on global scope for tests that reference it directly
  Object.defineProperty(globalThis, 'IntersectionObserver', {
    writable: true,
    configurable: true,
    value: MockIntersectionObserver,
  });

  require('mutationobserver-shim');
}

global.flushPromises = async () => {
  jest.useFakeTimers();
  const immediatePromise = new Promise((resolve) => setTimeout(resolve, 0));
  jest.runOnlyPendingTimers();

  await immediatePromise;
};

global.flushPromisesAct = async () => {
  jest.useFakeTimers();

  await act(async () => {
    const immediatePromise = new Promise((resolve) => setTimeout(resolve, 0));
    jest.runOnlyPendingTimers();

    await immediatePromise;
  });
};

if (typeof document !== 'undefined') ReactModal.setAppElement(document.body);

// Next/link accepts a child link and passes href props to it. This mocks that behaviour
jest.mock('next/link', () => {
  const Mock = ({ href, children }) => {
    const child = (Array.isArray(children) ? children[0] : children) || null;
    const newChild = {
      ...child,
      props: { ...child.props, href },
    };
    return <>{newChild}</>;
  };
  Mock.displayName = 'NextLink';
  return Mock;
});

// Next/head sets the header metadata for each page
jest.mock('next/head', () => {
  const Mock = ({ children }) => {
    return <>{children}</>;
  };
  Mock.displayName = 'NextHead';
  return {
    __esModule: true,
    default: Mock,
  };
});

jest.mock('pages/_app.page', () => {
  return {
    __esModule: true,
    wrapper: {
      getStaticProps: (callback) => callback,
      getServerSideProps: (callback) => callback,
    },
  };
});

jest.mock('@sentry/nextjs', () => {
  return {
    __esModule: true,
    captureException: jest.fn(),
    addBreadcrumb: jest.fn(),
    withScope: jest.fn((cb) => cb({ setExtra: jest.fn() })),
  };
});
