#!/usr/bin/env node

/**
 * <PERSON>ript to run yarn test:browser and capture all console output to a text file
 * Usage: node scripts/test-browser-with-console-output.js [output-file]
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get output file from command line or use default with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
const defaultOutputFile = `browser-test-console-output-${timestamp}.txt`;
const outputFile = process.argv[2] || defaultOutputFile;

// eslint-disable-next-line no-console
console.log('==========================================');
// eslint-disable-next-line no-console
console.log('Running yarn test:browser');
// eslint-disable-next-line no-console
console.log(`Console output will be saved to: ${outputFile}`);
// eslint-disable-next-line no-console
console.log('==========================================');

// Ensure output directory exists
const outputDir = path.dirname(outputFile);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Create write stream for the output file
const outputStream = fs.createWriteStream(outputFile);

// Spawn the jest process with the same configuration as yarn test:browser
const jestProcess = spawn('node', ['node_modules/.bin/jest', '--config', 'jest/jest.config.browser.ts', '--verbose', '--no-cache'], {
  env: {
    ...process.env,
    NODE_ENV: 'test',
    NODE_OPTIONS: '--no-deprecation',
  },
  stdio: ['inherit', 'pipe', 'pipe'],
});

// Write both stdout and stderr to the file and console
jestProcess.stdout.on('data', (data) => {
  process.stdout.write(data);
  outputStream.write(data);
});

jestProcess.stderr.on('data', (data) => {
  process.stderr.write(data);
  outputStream.write(data);
});

jestProcess.on('close', (code) => {
  outputStream.end();

  // eslint-disable-next-line no-console
  console.log('');
  // eslint-disable-next-line no-console
  console.log('==========================================');
  // eslint-disable-next-line no-console
  console.log('Test completed!');
  // eslint-disable-next-line no-console
  console.log(`Console output saved to: ${outputFile}`);

  // Get file size
  const stats = fs.statSync(outputFile);
  const fileSizeInBytes = stats.size;
  const fileSizeInKB = (fileSizeInBytes / 1024).toFixed(2);
  // eslint-disable-next-line no-console
  console.log(`File size: ${fileSizeInKB} KB`);
  // eslint-disable-next-line no-console
  console.log('==========================================');

  process.exit(code);
});

jestProcess.on('error', (error) => {
  // eslint-disable-next-line no-console
  console.error('Error running jest:', error);
  outputStream.end();
  process.exit(1);
});
