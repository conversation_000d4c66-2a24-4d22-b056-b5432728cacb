#!/bin/bash

# Script to run yarn test:browser and capture all console output to a text file
# Usage: ./scripts/test-browser-with-console-output.sh [output-file]

set -e

# Default output file with timestamp
DEFAULT_OUTPUT_FILE="browser-test-console-output-$(date +%Y%m%d-%H%M%S).txt"
OUTPUT_FILE="${1:-$DEFAULT_OUTPUT_FILE}"

echo "=========================================="
echo "Running yarn test:browser"
echo "Console output will be saved to: $OUTPUT_FILE"
echo "=========================================="

# Create output directory if it doesn't exist
mkdir -p "$(dirname "$OUTPUT_FILE")"

# Run the test command and capture all output (stdout and stderr)
# The --verbose flag ensures Jest shows all console.log statements
# The --no-cache flag ensures fresh test runs
NODE_ENV=test NODE_OPTIONS='--no-deprecation' yarn jest --config jest/jest.config.browser.ts --verbose --no-cache 2>&1 | tee "$OUTPUT_FILE"

echo ""
echo "=========================================="
echo "Test completed!"
echo "Console output saved to: $OUTPUT_FILE"
echo "File size: $(du -h "$OUTPUT_FILE" | cut -f1)"
echo "=========================================="