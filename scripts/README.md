# Test Console Output Scripts

This directory contains scripts to run browser tests and capture all console output to separate text files.

## Available Scripts

### 1. Bash <PERSON>ript
```bash
./scripts/test-browser-with-console-output.sh [output-file]
```

### 2. Node.js Script
```bash
node scripts/test-browser-with-console-output.js [output-file]
```

### 3. NPM Script
```bash
yarn test:browser:console [output-file]
```

## Usage Examples

### Default usage (auto-generated filename)
```bash
# Using bash script
./scripts/test-browser-with-console-output.sh

# Using Node.js script
node scripts/test-browser-with-console-output.js

# Using npm script
yarn test:browser:console
```

### Custom output filename
```bash
# Using bash script
./scripts/test-browser-with-console-output.sh my-test-output.txt

# Using Node.js script
node scripts/test-browser-with-console-output.js my-test-output.txt

# Using npm script
yarn test:browser:console my-test-output.txt
```

### With custom paths
```bash
# Save to logs directory
./scripts/test-browser-with-console-output.sh logs/test-output.txt

# Save to nested directory (will be created automatically)
node scripts/test-browser-with-console-output.js tests/logs/$(date +%Y-%m-%d)-output.txt
```

## What Gets Captured

The scripts capture all output from Jest including:
- Test results and summaries
- Console.log statements from test files
- Console.warn, console.error messages
- Jest verbose output
- Coverage information (if applicable)
- Any other stdout/stderr output

## Default Output Filename

If no filename is provided, the scripts will generate a default filename with timestamp:
```
browser-test-console-output-YYYYMMDD-HHMMSS.txt
```

## Features

- **Dual Output**: Console output is both displayed in the terminal AND saved to file
- **Automatic Directory Creation**: Output directories are created if they don't exist
- **File Size Reporting**: Shows the size of the captured output file
- **Error Handling**: Gracefully handles Jest process errors
- **Cross-Platform**: Both bash and Node.js versions for compatibility

## Jest Configuration

The scripts use the same configuration as `yarn test:browser`:
- Config: `jest/jest.config.browser.ts`
- Environment: `NODE_ENV=test`
- Node Options: `--no-deprecation`
- Flags: `--verbose --no-cache`

## Notes

- The `--verbose` flag ensures all console statements are captured
- The `--no-cache` flag ensures fresh test runs
- Output includes ANSI color codes for terminal formatting
- Large test suites may generate substantial output files